cd backend
npm install
npm run dev

https://everest-hackathon.onrender.com/api/auth/send-otp

request:{
    "phoneNumber":"+919347206960"
}

response:{
    "message": "OTP sent successfully",
    "sid": "VE6d9c8f376d7dc78a7bc79d96a90311c5"
}



https://everest-hackathon.onrender.com/api/auth/verify-otp


request:{
    "phoneNumber":"+919347206960",
    "otp":"761610"
}

response:{
    "message": "OTP verified successfully",
    "userId": "68e258409e2279a8a28fbb2f",
    "user": {
        "phoneNumber": "+919347206960",
        "isProfileComplete": false,
        "isVerified": true,
        "_id": "68e258409e2279a8a28fbb2f",
        "emergencyContacts": [],
        "createdAt": "2025-10-05T11:36:32.994Z",
        "__v": 0
    },
    "isProfileComplete": false
}




https://everest-hackathon.onrender.com/api/users/{userId}

request :{
   "name":"Meda Harsha Sri",
   "email":"<EMAIL>",
   "Address":"Nellore district",
   "emergencyContacts":[{
    "name":"Subramanyam",
    "phoneNumber":"+91 9347325589",
    "relationship":"Father"
   }]
}

response : {
    "message": "Profile updated successfully",
    "user": {
        "_id": "68e258409e2279a8a28fbb2f",
        "phoneNumber": "+919347206960",
        "isProfileComplete": true,
        "isVerified": true,
        "emergencyContacts": [
            {
                "name": "Subramanyam",
                "phoneNumber": "+91 9347325589",
                "relationship": "Father",
                "isPrimary": false,
                "canReceiveSosAlerts": true,
                "canTrackLocation": false,
                "_id": "68e323c8afffed643145eace"
            }
        ],
        "createdAt": "2025-10-05T11:36:32.994Z",
        "__v": 0,
        "email": "<EMAIL>",
        "name": "Meda Harsha Sri"
    }
}